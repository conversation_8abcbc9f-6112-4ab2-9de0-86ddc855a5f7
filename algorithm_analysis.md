# CPU任务调度算法分析

## 问题理解

### 输入格式
```
n                    // 冷却周期 (2 ≤ n < 10)
taskLength          // 任务字符串长度 (< 400)
tasks               // 实际任务字符串，如 "AAABBB"
```

### 问题约束
- 相同任务之间必须间隔至少 n 个时间单位
- 在冷却期间可以执行其他任务或空闲
- 求完成所有任务的最少时间

## 核心思路分析

### 关键观察
1. **频次最高的任务决定下界**：如果某个任务出现 k 次，那么至少需要 (k-1) × (n+1) + 1 个时间单位
2. **任务总数决定下界**：无论如何都需要至少 taskLength 个时间单位来执行所有任务
3. **最终答案是两个下界的最大值**

### 数学推导

假设：
- 最高频次任务出现 `maxCount` 次
- 有 `maxCountTasks` 种任务都达到最高频次

**情况1：需要空闲时间**
```
A _ _ A _ _ A _ _ A
```
如果 A 出现4次，n=2，则需要：
- 基础时间：(4-1) × (2+1) = 9
- 加上最后一个A：9 + 1 = 10

**情况2：任务种类足够多，无需空闲**
```
A B C A B C A B C A
```
如果有足够多不同的任务，可以填满所有空隙，此时答案就是任务总数。

### 公式推导

最终公式：
```cpp
minTime = max(taskLength, (maxCount - 1) * (n + 1) + maxCountTasks)
```

其中：
- `taskLength`：任务总数
- `maxCount`：最高频次
- `maxCountTasks`：达到最高频次的任务种类数
- `n`：冷却周期

## 算法实现

### 步骤
1. 统计每个任务的出现频次
2. 找到最大频次 `maxCount`
3. 统计有多少种任务达到最大频次 `maxCountTasks`
4. 应用公式计算结果

### 时间复杂度
- O(m)，其中 m 是任务字符串长度
- 统计频次：O(m)
- 排序：O(26 log 26) = O(1)

### 空间复杂度
- O(1)，只需要固定大小的数组存储26个字母的频次

## 示例分析

### 示例1
```
输入：n=2, tasks="AAABBB"
分析：
- A出现3次，B出现3次，maxCount=3，maxCountTasks=2
- 计算：max(6, (3-1)×(2+1)+2) = max(6, 8) = 8
- 执行序列：A B _ A B _ A B
```

### 示例2
```
输入：n=2, tasks="AAABBBCCC"
分析：
- A、B、C各出现3次，maxCount=3，maxCountTasks=3
- 计算：max(9, (3-1)×(2+1)+3) = max(9, 9) = 9
- 执行序列：A B C A B C A B C
```

### 示例3
```
输入：n=1, tasks="AABBCC"
分析：
- A、B、C各出现2次，maxCount=2，maxCountTasks=3
- 计算：max(6, (2-1)×(1+1)+3) = max(6, 5) = 6
- 执行序列：A B A C B C 或其他排列
```

## 边界情况

1. **n=0**：无冷却时间，答案就是任务总数
2. **只有一种任务**：答案是 `(count-1) × (n+1) + 1`
3. **任务种类很多**：通常答案就是任务总数
4. **极端情况**：一个任务出现很多次，其他任务很少

## 代码优化点

1. 使用数组而不是哈希表统计频次（字母只有26个）
2. 排序后从后往前找最大频次，避免多次遍历
3. 直接应用数学公式，避免模拟过程
