#include <vector>
#include <algorithm>
#include <queue>
#include <iostream>
using namespace std;

// Definition for a binary tree node.
struct TreeNode {
    int val;
    TreeNode *left;
    TreeNode *right;
    TreeNode() : val(0), left(nullptr), right(nullptr) {}
    TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}
    TreeNode(int x, TreeNode *left, TreeNode *right) : val(x), left(left), right(right) {}
};

class Solution {
private:
    int maxDiameter;
    
    // 计算以当前节点为根的子树的最大深度
    // 同时更新全局最大直径
    int maxDepth(TreeNode* root) {
        if (!root) return 0;
        
        // 递归计算左右子树的最大深度
        int leftDepth = maxDepth(root->left);
        int rightDepth = maxDepth(root->right);
        
        // 经过当前节点的最长路径长度 = 左子树深度 + 右子树深度
        int currentDiameter = leftDepth + rightDepth;
        
        // 更新全局最大直径
        maxDiameter = max(maxDiameter, currentDiameter);
        
        // 返回当前节点为根的子树的最大深度
        return max(leftDepth, rightDepth) + 1;
    }
    
public:
    int diameterOfBinaryTree(TreeNode* root) {
        maxDiameter = 0;
        maxDepth(root);
        return maxDiameter;
    }
    
    // 从数组构建二叉树（层序遍历方式，null用-1表示）
    TreeNode* buildTree(vector<int>& nums) {
        if (nums.empty() || nums[0] == -1) return nullptr;
        
        TreeNode* root = new TreeNode(nums[0]);
        queue<TreeNode*> q;
        q.push(root);
        
        int i = 1;
        while (!q.empty() && i < nums.size()) {
            TreeNode* node = q.front();
            q.pop();
            
            // 处理左子节点
            if (i < nums.size() && nums[i] != -1) {
                node->left = new TreeNode(nums[i]);
                q.push(node->left);
            }
            i++;
            
            // 处理右子节点
            if (i < nums.size() && nums[i] != -1) {
                node->right = new TreeNode(nums[i]);
                q.push(node->right);
            }
            i++;
        }
        
        return root;
    }
    
    // 打印二叉树（用于调试）
    void printTree(TreeNode* root) {
        if (!root) {
            cout << "Empty tree" << endl;
            return;
        }
        
        queue<TreeNode*> q;
        q.push(root);
        
        while (!q.empty()) {
            int size = q.size();
            for (int i = 0; i < size; i++) {
                TreeNode* node = q.front();
                q.pop();
                
                if (node) {
                    cout << node->val << " ";
                    q.push(node->left);
                    q.push(node->right);
                } else {
                    cout << "null ";
                }
            }
            cout << endl;
        }
    }
};

// 测试函数
int main() {
    Solution solution;
    
    // 测试用例1: [1,2,3,4,5]
    // 树的结构:
    //       1
    //      / \
    //     2   3
    //    / \
    //   4   5
    // 最长路径: 4 -> 2 -> 1 -> 3 (长度为3)
    vector<int> test1 = {1, 2, 3, 4, 5};
    TreeNode* root1 = solution.buildTree(test1);
    cout << "Test 1: ";
    solution.printTree(root1);
    cout << "Diameter: " << solution.diameterOfBinaryTree(root1) << endl << endl;
    
    // 测试用例2: [1,2]
    // 树的结构:
    //   1
    //  /
    // 2
    // 最长路径: 2 -> 1 (长度为1)
    vector<int> test2 = {1, 2};
    TreeNode* root2 = solution.buildTree(test2);
    cout << "Test 2: ";
    solution.printTree(root2);
    cout << "Diameter: " << solution.diameterOfBinaryTree(root2) << endl << endl;
    
    // 测试用例3: 图片中的例子 [1,2,-1,3,4]
    // 树的结构:
    //     1
    //    /
    //   2
    //  / \
    // 3   4
    // 最长路径: 3 -> 2 -> 4 (长度为2)
    vector<int> test3 = {1, 2, -1, 3, 4};
    TreeNode* root3 = solution.buildTree(test3);
    cout << "Test 3 (from image): ";
    solution.printTree(root3);
    cout << "Diameter: " << solution.diameterOfBinaryTree(root3) << endl;
    
    return 0;
}
