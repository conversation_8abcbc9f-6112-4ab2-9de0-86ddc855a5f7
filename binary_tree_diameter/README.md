# 二叉树的直径 (Diameter of Binary Tree)

## 问题描述
给定一棵二叉树，你需要计算它的直径长度。一棵二叉树的直径长度是任意两个结点路径长度中的最大值。这条路径可能穿过也可能不穿过根结点。

**注意：** 两结点之间的路径长度是以它们之间边的数目表示。

## 算法思路

### 核心思想
对于二叉树中的每一个节点，经过该节点的最长路径等于：
- 左子树的最大深度 + 右子树的最大深度

我们需要遍历每个节点，计算经过该节点的最长路径，并记录全局最大值。

### 算法步骤
1. 使用深度优先搜索(DFS)遍历二叉树
2. 对于每个节点，递归计算其左右子树的最大深度
3. 经过当前节点的直径 = 左子树深度 + 右子树深度
4. 更新全局最大直径
5. 返回当前子树的最大深度（用于上层递归）

### 时间复杂度
- **时间复杂度：** O(N)，其中N是二叉树中的节点数目。每个节点只被访问一次。
- **空间复杂度：** O(H)，其中H是二叉树的高度。递归调用的栈空间。

## 示例

### 示例1
```
输入: [1,2,3,4,5]
      1
     / \
    2   3
   / \
  4   5

输出: 3
解释: 最长路径为 [4,2,1,3] 或者 [5,2,1,3]，长度为3
```

### 示例2
```
输入: [1,2]
  1
 /
2

输出: 1
解释: 最长路径为 [2,1]，长度为1
```

### 示例3（图片中的例子）
```
输入: [1,2,null,3,4]
    1
   /
  2
 / \
3   4

输出: 2
解释: 最长路径为 [3,2,4]，长度为2
```

## 关键点
1. **直径不一定经过根节点**：最长路径可能完全在某个子树中
2. **深度vs直径**：节点的深度是从该节点到叶子节点的最长路径，而直径是任意两点间的最长路径
3. **边数vs节点数**：题目要求的是边的数量，不是节点数量

## 代码实现要点
- 使用全局变量记录最大直径
- 递归函数返回当前子树的最大深度
- 在递归过程中更新全局最大直径
- 处理空节点的边界情况
