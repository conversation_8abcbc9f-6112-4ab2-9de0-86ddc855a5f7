#include <iostream>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <algorithm>
using namespace std;

// 方法一：模拟法（使用优先队列）
int solutionSimulation(string tasks, int n) {
    // 统计每个任务的频次
    unordered_map<char, int> taskCount;
    for (char task : tasks) {
        taskCount[task]++;
    }
    
    // 使用优先队列存储任务频次
    priority_queue<int> maxHeap;
    for (auto& pair : taskCount) {
        maxHeap.push(pair.second);
    }
    
    int time = 0;
    
    // 模拟任务执行过程
    while (!maxHeap.empty()) {
        vector<int> temp;
        int cycle = n + 1; // 一个完整的冷却周期
        
        // 在一个冷却周期内尽可能多地执行任务
        for (int i = 0; i < cycle && (!maxHeap.empty() || !temp.empty()); i++) {
            if (!maxHeap.empty()) {
                int count = maxHeap.top();
                maxHeap.pop();
                if (count > 1) {
                    temp.push_back(count - 1);
                }
            }
            time++;
        }
        
        // 将剩余的任务重新放回堆中
        for (int count : temp) {
            maxHeap.push(count);
        }
    }
    
    return time;
}

int main() {
    int n;
    int taskLength;
    string tasks;
    
    // 读取输入
    cin >> n;
    cin >> taskLength;
    cin >> tasks;
    
    // 使用数学公式法求解（更高效）
    int result = solutionMath(tasks, n);
    
    cout << result << endl;
    
    return 0;
}


// 方法二：数学公式法（推荐）
int solutionMath(string tasks, int n) {
    // 统计每个字符的频次
    vector<int> count(26, 0);
    for (char task : tasks) {
        count[task - 'A']++;
    }
    
    // 排序，找到最大频次
    sort(count.begin(), count.end());
    
    int maxCount = count[25]; // 最大频次
    int maxCountTasks = 1;    // 具有最大频次的任务种类数
    
    // 统计有多少种任务具有最大频次
    for (int i = 24; i >= 0; i--) {
        if (count[i] == maxCount) {
            maxCountTasks++;
        } else {
            break;
        }
    }
    
    // 核心公式：
    // 最少时间 = max(任务总数, (最大频次-1) * (n+1) + 最大频次任务种类数)
    int minTime = (maxCount - 1) * (n + 1) + maxCountTasks;
    
    return max((int)tasks.length(), minTime);
}
