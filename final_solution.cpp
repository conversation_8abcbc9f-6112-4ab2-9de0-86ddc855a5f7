#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
using namespace std;

int main() {
    int n, taskLength;
    string tasks;
    
    // 读取输入
    cin >> n >> taskLength >> tasks;
    
    // 统计每个字符的频次
    vector<int> count(26, 0);
    for (int i = 0; i < tasks.length(); i++) {
        count[tasks[i] - 'A']++;
    }
    
    // 排序找到最大频次
    sort(count.begin(), count.end());
    
    int maxCount = count[25];  // 最大频次
    int maxCountTasks = 0;     // 具有最大频次的任务种类数
    
    // 统计有多少种任务具有最大频次
    for (int i = 25; i >= 0 && count[i] == maxCount; i--) {
        maxCountTasks++;
    }
    
    // 核心公式：max(任务总数, (最大频次-1) * (n+1) + 最大频次任务种类数)
    int result = max((int)tasks.length(), (maxCount - 1) * (n + 1) + maxCountTasks);
    
    cout << result << endl;
    
    return 0;
}
